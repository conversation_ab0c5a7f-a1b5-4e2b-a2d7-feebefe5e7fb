package com.ydy.dingtalk.oa.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiGettokenRequest;
import com.dingtalk.api.request.OapiV2UserGetRequest;
import com.dingtalk.api.request.OapiV2UserGetuserinfoRequest;
import com.dingtalk.api.response.OapiGettokenResponse;
import com.dingtalk.api.response.OapiV2UserGetResponse;
import com.dingtalk.api.response.OapiV2UserGetuserinfoResponse;
import com.taobao.api.ApiException;
import com.ydy.dingtalk.oa.entity.*;
import com.ydy.dingtalk.oa.service.EmployeeScheduleService;
import com.ydy.dingtalk.oa.service.OaCalendarCacheService;
import com.ydy.dingtalk.oa.service.OaCalendarService;
import com.ydy.dingtalk.oa.utils.DateTimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.upms.model.EruptMenu;
import xyz.erupt.upms.model.EruptRole;
import xyz.erupt.upms.model.EruptUser;
import xyz.erupt.upms.platform.EruptOption;
import xyz.erupt.upms.platform.EruptOptions;
import xyz.erupt.upms.service.EruptPlatformService;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Description
 * <AUTHOR>
 * @Create 2025/1/15
 */
@EruptOptions({
        @EruptOption(name = OaInfoController.CALENDAR_SIGN, value = "内部会议,外部参会,培训交流", desc = "日程标签"),
        @EruptOption(name = OaInfoController.OA_MEETING_ROOMS, value = "光明顶,燕子坞,桃花岛,飘渺峰,聚贤堂,会议室一,会议室二,会议室三,会议室四,会议室五", desc = "会议室名称"),
        @EruptOption(name = OaInfoController.OA_ACCOUNT_SIGN, value = ",\"云顶云工作号（新）\"", desc = "oa账号名称"),
        @EruptOption(name = OaInfoController.DELETE_CONTENT, value = "开始前15分钟, 应用弹窗提醒我, 评论, 拒绝, 暂定, 接受", desc = "剔除内容"),
        @EruptOption(name = OaInfoController.OA_ROOT_SWITCH, value = "on", desc = "允许角色按钮"),
})
@Slf4j
@RestController
@RequestMapping("erupt-api")
public class OaInfoController {

    public static final String CALENDAR_SIGN = "CALENDAR_SIGN";
    public static final String OA_ACCOUNT_SIGN = "OA_ACCOUNT_SIGN";
    public static final String OA_MEETING_ROOMS = "OA_MEETING_ROOMS";
    public static final String DELETE_CONTENT = "DELETE_CONTENT";
    public static final String OA_ROOT_SWITCH = "OA_ROOT_SWITCH";

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private EruptDao eruptDao;

    @Resource
    private EruptUserService eruptUserService;

    @Resource
    private EruptPlatformService eruptPlatformService;

    @Resource
    private OaCalendarCacheService oaCalendarCacheService;

    @Resource
    private OaCalendarService oaCalendarService;

    @Resource
    private EmployeeScheduleService employeeScheduleService ;

    // 添加到类成员区域
    private static final String[] CHINESE_NUMBERS = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九", "十"};

    @RequestMapping("oa/isPermit")
    public EruptApiModel isPermit() {
        boolean isPermit = false;
        String rootSwitch = eruptPlatformService.getOption(OaInfoController.OA_ROOT_SWITCH).getAsString();
        if (rootSwitch.equals("off")) {
            log.info("未开启权限校验");
            return EruptApiModel.successApi("未开启权限校验", isPermit);
        }
        EruptUser user = eruptUserService.getCurrentEruptUser();
        if (user == null) {
            log.info("用户未登录");
            return EruptApiModel.successApi("用户未登录", isPermit);
        }
        // 校验用户是否有权限：菜单：OA外出
        Set<EruptRole> roles = user.getRoles();
        for (EruptRole role : roles) {
            Set<EruptMenu> menus = role.getMenus();
            for (EruptMenu menu : menus) {
                if (menu.getCode().equals("PutOaInfoRequest@VIEW_DETAIL")) {
                    isPermit = true;
                    // log.info("用户存在权限");
                    return EruptApiModel.successApi("用户存在权限", isPermit);
                }
            }
        }
        log.info("用户没有权限");
        return EruptApiModel.successApi("用户没有权限", isPermit);
    }

    @RequestMapping("oa/isRefresh")
    public Map<String, Object> isRefresh() {
        // 1、初始化数据
        Map<String, Object> map = new HashMap<>();
        // 2、获取OA外出信息
        List<PutOaInfoRequest> putOaInfoRequests = eruptDao.queryEntityList(PutOaInfoRequest.class, "approval_status NOT LIKE '%拒绝%' AND approval_status NOT LIKE '%已撤销%'");
        // 3、获取日程信息
        List<OaCalendarRequest> oaCalendarRequests = eruptDao.queryEntityList(OaCalendarRequest.class, "sign IS NOT NULL AND subject NOT LIKE '%已取消%'");
        // 4、存入集合
        map.put("oaInfo", putOaInfoRequests);
        map.put("oaCalendar", oaCalendarRequests);
        // 5、获取redisTemplate中key为oaList的数据，并判断是否存在
        Object o = redisTemplate.opsForValue().get("oaMap");
        // 6、如果不存在，将下面的数据存入redis，如果存在，则清除里面的数据，然后存入新的数据
        if (o != null) {
            redisTemplate.delete("oaMap");
        }
        // 7、存入新数据
        redisTemplate.opsForValue().set("oaMap", map);
        return map;
    }

    @RequestMapping("oa/queryOaInfo")
    public EruptApiModel queryOaInfo() {
        // 1、初始化返回集合
        Map<String, Object> map;

        // 3、redis获取数据
        Object o = redisTemplate.opsForValue().get("oaMap");
        // 4、判断是否为空
        if (o != null) {
            map = (Map<String, Object>) o;
        } else {
            map = this.isRefresh();
        }
        return EruptApiModel.successApi(map);
    }

    @Transactional
    @RequestMapping("oa/putOaInfo")
    public String putOaInfo(@RequestBody String raw) {
        try {
            // 解析请求数据
            AnalyzeOaInfoRequest analyzeOaInfoRequest = JSON.parseObject(raw, AnalyzeOaInfoRequest.class);
            String data = analyzeOaInfoRequest.getTableText();

            // 提取所有字段数据
            Map<String, String> extractedData = extractAllFields(data);
            String approvalNumber = extractedData.get("approvalNumber");
            String approvalStatus = extractedData.get("approvalStatus");

            // 使用参数化查询避免SQL注入
            PutOaInfoRequest oaInfoQuery = eruptDao.queryEntity(PutOaInfoRequest.class, "approval_Number = '" + approvalNumber + "'");

            // 处理现有记录
            if (oaInfoQuery != null) {
                // 处理撤销或修改状态
                if (isStatusCanceledOrModified(approvalStatus)) {
                    eruptDao.delete(oaInfoQuery);
                    // 创建新实体并保存
                    if (approvalStatus.contains("已修改")) {
                        PutOaInfoRequest newEntity = updateOaInfo(extractedData, analyzeOaInfoRequest.getType());
                        eruptDao.merge(newEntity);
                        return "已修改";
                    }
                    return "已删除";
                }

                // 如果数据库中的记录已经是撤销或修改状态，则跳过
                if (isStatusCanceledOrModified(oaInfoQuery.getApprovalStatus())) {
                    return "已跳过";
                }

                // 更新现有记录
                eruptDao.delete(oaInfoQuery);
                PutOaInfoRequest newEntity = updateOaInfo(extractedData, analyzeOaInfoRequest.getType());
                eruptDao.merge(newEntity);
                return "已更新";
            }

            // 创建新记录
            PutOaInfoRequest newRecord = createNewOaInfo(extractedData, analyzeOaInfoRequest.getType());
            eruptDao.merge(newRecord);
            return "已存入";
        } catch (Exception e) {
            log.error("处理OA信息时发生错误", e);
            throw new RuntimeException("处理OA信息失败: " + e.getMessage());
        }
    }

    // 提取所有字段数据
    private Map<String, String> extractAllFields(String data) {
        Map<String, String> fields = new HashMap<>();
        fields.put("submitter", extractData(data, "(?<=^)[^\n]+", 0));
        fields.put("approvalStatus", extractData(data, "(?<=\n\n)[^\n]+", 0));
        fields.put("approvalNumber", extractData(data, "(?<=审批编号\\n)[0-9]+", 0));
        fields.put("department", extractData(data, "(?<=所在部门\\n)[^\n]+", 0));
        fields.put("tripReason", extractData(data, "(?<=出差事由\\n)[^\n]+", 0));
        fields.put("transport", extractData(data, "(?<=交通工具\\n)[^\n]+", 0));
        fields.put("roundTrip", extractData(data, "(?<=单程往返\\n)[^\n]+", 0));
        fields.put("departure", extractData(data, "(?<=出发地\\n)[^\n]+", 0));
        fields.put("destination", extractData(data, "(?<=目的地\\n)[^\n]+", 0));
        fields.put("startTime", extractData(data, "(?<=开始时间\\n)[^\n]+", 0));
        fields.put("endTime", extractData(data, "(?<=结束时间\\n)[^\n]+", 0));
        fields.put("duration", extractData(data, "(?<=时长（小时）\\n)[^\n]+", 0));
        fields.put("tripDays", extractData(data, "(?<=出差天数\\n)[^\n]+", 0));
        fields.put("companion", extractData(data, "(?<=同行人\\n)[^\n]+", 0));
        fields.put("travelMode", extractData(data, "(?<=出行方式\\n)[^\n]+", 0));
        fields.put("distance", extractData(data, "(?<=公里数\\n)[^\n]+", 0));
        fields.put("escort", extractData(data, "(?<=陪访人\\n)[^\n]+", 0));

        // 检查是否存在老格式的公司信息
        /*String oldCompanyName = extractData(data, "(?<=公司名称\\n)[^\n]+", 0);
        String oldCompanyAddress = extractData(data, "(?<=公司地址\\n)[^\n]+", 0);*/

        // 检查是否存在新格式的多公司信息
        String visitCompaniesCount = extractData(data, "(?<=拜访几家客户\\n)[^\n]+", 0);

        // 处理多公司情况
        /*if (!"无".equals(visitCompaniesCount)) {*/
        // 使用JSONArray存储多公司信息
        JSONArray companiesArray = new JSONArray();

        // 截取表格部分内容
        String tableContent = "";
        int tableStartIndex = data.indexOf("表格");
        // 优先检查"到达地点签到"作为结束标记，如果没有再检查"流程"
        int signInIndex = data.indexOf("到达地点签到", tableStartIndex);
        int processIndex = data.indexOf("流程", tableStartIndex);

        // 确定表格结束位置
        int tableEndIndex;
        if (signInIndex >= 0) {
            // 如果找到"到达地点签到"，使用它作为结束标记
            tableEndIndex = signInIndex;
            log.info("使用'到达地点签到'作为表格结束标记");
        } else if (processIndex >= 0) {
            // 如果没有找到"到达地点签到"但找到了"流程"，使用"流程"作为结束标记
            tableEndIndex = processIndex;
            log.info("使用'流程'作为表格结束标记");
        } else {
            // 如果两者都没找到，设置为-1表示未找到结束位置
            tableEndIndex = -1;
            log.warn("未找到表格结束标记");
        }

        if (tableStartIndex >= 0 && tableEndIndex >= 0) {
            tableContent = data.substring(tableStartIndex, tableEndIndex).trim();
            log.info("提取到的表格内容: \n{}", tableContent);  // 添加日志
        }

        // 检测表格格式（判断是三列还是五列）
        // 使用更灵活的方式检测表格格式，不依赖于精确的标题匹配

        // 检测表格格式（判断是三列、四列还是五列）
        boolean isThreeColumnFormat = false;
        boolean isFourColumnFormat = false;
        String headerLine = "";
        // 提取表格第一行（标题行）
        Pattern headerPattern = Pattern.compile("表格\\s*\\n([^\\n]+)", Pattern.MULTILINE);
        Matcher headerMatcher = headerPattern.matcher(tableContent);
        if (headerMatcher.find()) {
            headerLine = headerMatcher.group(1).trim();
            // 检查标题行中的列数
            isThreeColumnFormat = !headerLine.contains("客户姓名") && !headerLine.contains("客户职位");
            isFourColumnFormat = headerLine.contains("客户姓名") && !headerLine.contains("客户职位");
            log.info("检测到表格标题: {}，是否为三列格式: {}，是否为四列格式: {}", headerLine, isThreeColumnFormat, isFourColumnFormat);
        } else {
            log.warn("未能匹配到表格标题行");
        }

        // 使用正则表达式匹配表格中的每一行数据
        Pattern tableRowPattern;
        if (isThreeColumnFormat) {
            // 三列格式：序号、公司名称、公司地址、外出事由
            tableRowPattern = Pattern.compile("\\n\\s*(\\d+)\\s*\\n\\s*([^\\n]+?)\\s*\\n\\s*([^\\n]+?)\\s*\\n\\s*([^\\n]+?)\\s*(?=\\n\\d|$)", Pattern.MULTILINE);
        } else if (isFourColumnFormat) {
            // 四列格式：序号、公司名称、公司地址、客户姓名、外出事由
            tableRowPattern = Pattern.compile("\\n\\s*(\\d+)\\s*\\n\\s*([^\\n]+?)\\s*\\n\\s*([^\\n]+?)\\s*\\n\\s*([^\\n]+?)\\s*\\n\\s*([^\\n]+?)\\s*(?=\\n\\d|$)", Pattern.MULTILINE);
        } else {
            // 五列格式：序号、公司名称、公司地址、客户姓名、客户职位、外出事由
            tableRowPattern = Pattern.compile("\\n\\s*(\\d+)\\s*\\n\\s*([^\\n]+?)\\s*\\n\\s*([^\\n]+?)\\s*\\n\\s*([^\\n]+?)\\s*\\n\\s*([^\\n]+?)\\s*\\n\\s*([^\\n]+?)\\s*(?=\\n\\d|$)", Pattern.MULTILINE);
        }
        Matcher tableRowMatcher = tableRowPattern.matcher(tableContent);

        Map<String, Integer> indexMap = new HashMap<>(); // 用于记录每个序号对应的数组索引

        while (tableRowMatcher.find()) {
            String index = tableRowMatcher.group(1).trim();
            String companyName = tableRowMatcher.group(2).trim();
            String companyAddress = tableRowMatcher.group(3).trim();

            log.info("提取到的行数据 - 序号: {}, 公司名称: {}, 公司地址: {}", index, companyName, companyAddress);  // 添加日志


            String clientName = "无";
            String clientPosition = "无";
            String outReason;

            if (isThreeColumnFormat) {
                // 三列格式：外出事由在第4组
                outReason = tableRowMatcher.group(4).trim();
            } else if (isFourColumnFormat) {
                // 四列格式：客户姓名在第4组，外出事由在第5组
                clientName = tableRowMatcher.group(4).trim();
                outReason = tableRowMatcher.group(5).trim();
                log.info("四列格式 - 客户姓名: {}, 外出事由: {}", clientName, outReason);  // 添加日志
            } else {
                // 五列格式：客户信息和外出事由在4-6组
                clientName = tableRowMatcher.group(4).trim();
                clientPosition = tableRowMatcher.group(5).trim();
                outReason = tableRowMatcher.group(6).trim();
            }

            // 将数字序号转为中文数字形式，保持兼容性
            String chineseIndex = normalizeToChineseNumber(index);

            // 创建JSON对象并添加到数组
            JSONObject company = new JSONObject();
            company.put("index", chineseIndex);
            company.put("name", companyName);
            company.put("address", companyAddress);
            company.put("clientName", clientName);
            company.put("clientPosition", clientPosition);
            company.put("outReason", outReason); // 新增外出事由字段

            companiesArray.add(company);

            // 记录索引，用于后续获取第一家公司信息
            indexMap.put(index, companiesArray.size() - 1);
        }

        // 只有当有多家公司时才存储multipleCompanies字段
        if (companiesArray.size() > 1) {
            // 将JSONArray转换为字符串后存储
            fields.put("multipleCompanies", companiesArray.toJSONString());
        } else {
            // 如果只有一家或没有公司，设置multipleCompanies为"无"
            fields.put("multipleCompanies", "无");
        }

        // 为了向后兼容，保留第一家公司作为主要公司信息
        if (!companiesArray.isEmpty()) {
            // 获取序号为1的公司或第一个公司
            JSONObject firstCompany = indexMap.containsKey("1") ?
                    companiesArray.getJSONObject(indexMap.get("1")) :
                    companiesArray.getJSONObject(0);

            fields.put("companyName", firstCompany.getString("name"));
            fields.put("companyAddress", firstCompany.getString("address"));
            fields.put("clientName", firstCompany.getString("clientName"));
            fields.put("clientPosition", firstCompany.getString("clientPosition"));

            // 如果出差事由为"无"，使用第一家公司的外出事由
            if (fields.get("tripReason").equals("无") && firstCompany.containsKey("outReason")) {
                fields.put("tripReason", firstCompany.getString("outReason"));
            }
        } else {
            fields.put("companyName", "无");
            fields.put("companyAddress", "无");
            fields.put("clientName", "无");
            fields.put("clientPosition", "无");
        }
            /*} else {
                // 原有的处理逻辑，支持老格式的数据
                // 修改正则表达式，支持更广泛的数字格式
                // 支持从一到九十九的中文数字和阿拉伯数字
                Pattern companyNamePattern = Pattern.compile("公司((?:[一二三四五六七八九]?十)?[一二三四五六七八九]?|[1-9][0-9]?|100)名称\\n([^\n]+)", Pattern.MULTILINE);
                Pattern companyAddressPattern = Pattern.compile("公司((?:[一二三四五六七八九]?十)?[一二三四五六七八九]?|[1-9][0-9]?|100)地址\\n([^\n]+)", Pattern.MULTILINE);

                // 添加多客户信息提取模式
                Pattern clientNamePattern = Pattern.compile("客户((?:[一二三四五六七八九]?十)?[一二三四五六七八九]?|[1-9][0-9]?|100)姓名\\n([^\n]+)", Pattern.MULTILINE);
                Pattern clientPositionPattern = Pattern.compile("客户((?:[一二三四五六七八九]?十)?[一二三四五六七八九]?|[1-9][0-9]?|100)职位\\n([^\n]+)", Pattern.MULTILINE);

                Matcher namesMatcher = companyNamePattern.matcher(data);
                Matcher addressesMatcher = companyAddressPattern.matcher(data);
                Matcher clientNamesMatcher = clientNamePattern.matcher(data);
                Matcher clientPositionsMatcher = clientPositionPattern.matcher(data);

                Map<String, String> companyNames = new HashMap<>();
                Map<String, String> companyAddresses = new HashMap<>();
                Map<String, String> clientNames = new HashMap<>();
                Map<String, String> clientPositions = new HashMap<>();

                while (namesMatcher.find()) {
                    String key = normalizeToChineseNumber(namesMatcher.group(1));
                    companyNames.put(key, namesMatcher.group(2).trim());
                }

                while (addressesMatcher.find()) {
                    String key = normalizeToChineseNumber(addressesMatcher.group(1));
                    companyAddresses.put(key, addressesMatcher.group(2).trim());
                }

                while (clientNamesMatcher.find()) {
                    String key = normalizeToChineseNumber(clientNamesMatcher.group(1));
                    clientNames.put(key, clientNamesMatcher.group(2).trim());
                }

                while (clientPositionsMatcher.find()) {
                    String key = normalizeToChineseNumber(clientPositionsMatcher.group(1));
                    clientPositions.put(key, clientPositionsMatcher.group(2).trim());
                }

                // 创建有序的公司键列表，确保按照顺序排列
                List<String> sortedCompanyKeys = new ArrayList<>(companyNames.keySet());
                Collections.sort(sortedCompanyKeys, (a, b) -> {
                    // 尝试比较中文数字的数值
                    int indexA = getChineseNumberValue(a);
                    int indexB = getChineseNumberValue(b);
                    return Integer.compare(indexA, indexB);
                });

                // 将收集到的公司信息组合成JSON数组
                for (String key : sortedCompanyKeys) {
                    JSONObject company = new JSONObject();
                    company.put("index", key);  // 保留索引信息
                    company.put("name", companyNames.get(key));
                    company.put("address", companyAddresses.getOrDefault(key, "无"));

                    // 添加对应的客户信息
                    if (clientNames.containsKey(key)) {
                        company.put("clientName", clientNames.get(key));
                        company.put("clientPosition", clientPositions.getOrDefault(key, "无"));
                    }

                    companiesArray.add(company);
                }

                // 将JSONArray转换为字符串后存储（因为Map<String, String>只能存储字符串）
                fields.put("multipleCompanies", companiesArray.toJSONString());

                // 为了向后兼容，保留第一家公司作为主要公司信息
                if (!sortedCompanyKeys.isEmpty()) {
                    String firstKey = sortedCompanyKeys.get(0);
                    fields.put("companyName", companyNames.get(firstKey));
                    fields.put("companyAddress", companyAddresses.getOrDefault(firstKey, "无"));
                    fields.put("clientName", clientNames.getOrDefault(firstKey, "无"));
                    fields.put("clientPosition", clientPositions.getOrDefault(firstKey, "无"));
                } else {
                    fields.put("companyName", "无");
                    fields.put("companyAddress", "无");
                    fields.put("clientName", "无");
                    fields.put("clientPosition", "无");
                }
            }*/
        /*} else {
            // 使用老格式的单公司信息
            fields.put("companyName", oldCompanyName);
            fields.put("companyAddress", oldCompanyAddress);
            fields.put("multipleCompanies", "无");

            // 使用老格式的单客户信息或者使用从表单中提取的客户信息
            String clientName = extractData(data, "(?<=客户姓名\\n)[^\n]+", 0);
            String clientPosition = extractData(data, "(?<=客户职位\\n)[^\n]+", 0);
            fields.put("clientName", clientName);
            fields.put("clientPosition", clientPosition);
        }*/

        // 如果出差事由为"无"，尝试从外出事由获取
        if (fields.get("tripReason").equals("无")) {
            fields.put("tripReason", extractData(data, "(?<=外出事由\\n)[^\n]+", 0));
        }

        return fields;
    }

    // 将阿拉伯数字转换为中文数字
    private String normalizeToChineseNumber(String number) {
        try {
            // 尝试解析为阿拉伯数字
            int num = Integer.parseInt(number);
            if (num >= 0 && num <= 10) {
                return CHINESE_NUMBERS[num];
            } else if (num > 10 && num < 100) {
                // 处理11-99的数字
                int tens = num / 10;
                int ones = num % 10;
                if (tens == 1) {
                    // 11-19
                    return ones == 0 ? "十" : "十" + CHINESE_NUMBERS[ones];
                } else {
                    // 20-99
                    return CHINESE_NUMBERS[tens] + "十" + (ones == 0 ? "" : CHINESE_NUMBERS[ones]);
                }
            } else if (num == 100) {
                return "一百";
            }
        } catch (NumberFormatException e) {
            // 如果不是阿拉伯数字，检查是否是已经格式化好的中文数字
            if (isChineseNumber(number)) {
                return number;
            }
        }
        return number;
    }

    // 判断是否是有效的中文数字
    private boolean isChineseNumber(String number) {
        // 简单检查是否只包含中文数字字符
        return number.matches("[零一二三四五六七八九十百]+");
    }

    // 修改方法签名，返回新的实体
    private PutOaInfoRequest updateOaInfo(Map<String, String> data, String type) {
        String destination = data.get("destination");
        String companyAddress = data.get("companyAddress");

        // 处理目的地
        if (destination != null && destination.length() >= 3) {
            String prefix = destination.substring(0, 3);
            if (prefix.contains("天津") || prefix.contains("天津市")) {
                destination = destination.substring(3);
            }
        }

        // 处理公司地址
        if (companyAddress != null && companyAddress.length() >= 3) {
            String prefix = companyAddress.substring(0, 3);
            if (prefix.contains("天津") || prefix.contains("天津市")) {
                companyAddress = companyAddress.substring(3);
            }
        }

        return PutOaInfoRequest.builder()
                .submitter(data.get("submitter"))
                .approvalStatus(data.get("approvalStatus"))
                .approvalNumber(data.get("approvalNumber"))
                .department(data.get("department"))
                .tripReason(data.get("tripReason"))
                .transport(data.get("transport"))
                .roundTrip(data.get("roundTrip"))
                .departure(data.get("departure"))
                .destination("无".equals(String.valueOf(destination)) ? null : destination)
                .startTime(data.get("startTime"))
                .endTime(data.get("endTime"))
                .duration(data.get("duration"))
                .tripDays(data.get("tripDays"))
                .companion(data.get("companion"))
                .travelMode(data.get("travelMode"))
                .distance(data.get("distance"))
                .escort(data.get("escort"))
                .companyName(data.get("companyName"))
                .companyAddress("无".equals(String.valueOf(companyAddress)) ? null : companyAddress)
                .multipleCompanies("无".equals(data.get("multipleCompanies")) ? null : data.get("multipleCompanies"))
                .clientName(data.get("clientName"))
                .clientPosition(data.get("clientPosition"))
                .oaType(type)
                .build();
    }

    // 创建新的OA信息对象
    private PutOaInfoRequest createNewOaInfo(Map<String, String> data, String type) {
        String destination = data.get("destination");
        String companyAddress = data.get("companyAddress");

        // 处理目的地
        if (destination != null && destination.length() >= 3) {
            String prefix = destination.substring(0, 3);
            if (prefix.contains("天津") || prefix.contains("天津市")) {
                destination = destination.substring(3);
            }
        }

        // 处理公司地址
        if (companyAddress != null && companyAddress.length() >= 3) {
            String prefix = companyAddress.substring(0, 3);
            if (prefix.contains("天津") || prefix.contains("天津市")) {
                companyAddress = companyAddress.substring(3);
            }
        }

        return PutOaInfoRequest.builder()
                .submitter(data.get("submitter"))
                .approvalStatus(data.get("approvalStatus"))
                .approvalNumber(data.get("approvalNumber"))
                .department(data.get("department"))
                .tripReason(data.get("tripReason"))
                .transport(data.get("transport"))
                .roundTrip(data.get("roundTrip"))
                .departure(data.get("departure"))
                .destination("无".equals(String.valueOf(destination)) ? null : destination)
                .startTime(data.get("startTime"))
                .endTime(data.get("endTime"))
                .duration(data.get("duration"))
                .tripDays(data.get("tripDays"))
                .companion(data.get("companion"))
                .travelMode(data.get("travelMode"))
                .distance(data.get("distance"))
                .escort(data.get("escort"))
                .companyName(data.get("companyName"))
                .companyAddress("无".equals(String.valueOf(companyAddress)) ? null : companyAddress)
                .multipleCompanies("无".equals(data.get("multipleCompanies")) ? null : data.get("multipleCompanies"))
                .clientName(data.get("clientName"))
                .clientPosition(data.get("clientPosition"))
                .oaType(type)
                .build();
    }

    // 检查状态是否为撤销或修改
    private boolean isStatusCanceledOrModified(String status) {
        return status != null && (status.contains("已撤销") || status.contains("已修改"));
    }

    private static String extractData(String text, String regex, int group) {
        Pattern pattern = Pattern.compile(regex, Pattern.MULTILINE);
        Matcher matcher = pattern.matcher(text);
        if (matcher.find()) {
            String result = matcher.group(group).trim();
            // 添加日志，仅对时间字段进行记录
            if (regex.contains("时间")) {
                log.info("提取时间字段 - 正则表达式: {}, 提取结果: {}", regex, result);
            }
            return result;
        }
        return "无";
    }

    // 添加新的验证方法
    private boolean isValidLocation(String location, List<String> participants) {
        // 1. 检查是否是指定的会议室
        String roomSign = eruptPlatformService.getOption(OaInfoController.OA_MEETING_ROOMS).getAsString();
        String[] meetingRooms = roomSign.split(",");
        for (String room : meetingRooms) {
            if (location.contains(room)) {
                return true;
            }
        }

        // 2. 检查是否包含"茶室"
        if (location.contains("茶室")) {
            return true;
        }

        // 3. 检查是否包含参与人员的姓名
        for (String participant : participants) {
            if (location.contains(participant)) {
                return false;
            }
        }
        return true;
    }

    @Transactional
    @RequestMapping("oa/saveSchedule")
    public String putOaCalendarSchedule(@RequestBody String raw) {
        try {
            // 解析请求数据
            AnalyzeOaInfoRequest analyzeRequest = JSON.parseObject(raw, AnalyzeOaInfoRequest.class);

            // 创建日程请求对象
            OaCalendarRequest request = oaCalendarService.createCalendarRequest(analyzeRequest);

            // 检查是否已存在
            if (oaCalendarService.isCalendarExists(request)) {
                // log.error("日程已存在");
                return "已存在";
            }

            // 保存数据
            oaCalendarService.saveCalendar(request);
            return "success";
        } catch (Exception e) {
            log.error("处理OA信息时发生错误", e);
            throw new RuntimeException("处理OA信息失败: " + e.getMessage());
        }
    }

    /**
     * 缓存日程数据
     */
    @RequestMapping("oa/cacheSchedule")
    public EruptApiModel cacheSchedule(@RequestBody String raw) {
        try {
            // 解析请求数据
            AnalyzeOaInfoRequest request = JSON.parseObject(raw, AnalyzeOaInfoRequest.class);

            // 从日程信息中提取日期
            String dateStr = extractDateFromSchedule(request.getCalendarSchedule());
            
            // 检查是否有多个日期（跨天日期）
            if (dateStr.contains(",")) {
                String[] dates = dateStr.split(",");
                // 为每个日期缓存数据
                for (String date : dates) {
                    oaCalendarCacheService.cacheCalendarData(date, request);
                }
            } else {
                // 单个日期，直接缓存
                oaCalendarCacheService.cacheCalendarData(dateStr, request);
            }

            return EruptApiModel.successApi("数据已缓存");
        } catch (Exception e) {
            log.error("缓存日程数据失败", e);
            return EruptApiModel.errorApi("缓存失败: " + e.getMessage());
        }
    }

    /**
     * 处理所有缓存的日程数据
     */
    @Transactional
    @RequestMapping("oa/processAllSchedules")
    public EruptApiModel processAllSchedules() {
        BatchProcessResult totalResult = new BatchProcessResult();

        try {
            // 1. 首先检查是否有缓存数据
            Set<Object> dates = oaCalendarCacheService.getAllPendingDates();
            if (dates == null || dates.isEmpty()) {
                return EruptApiModel.successApi("没有待处理的数据，为确保数据安全，不执行清理操作");
            }

            // 2. 统计缓存数据总数
            int totalCacheCount = 0;
            Map<String, Integer> dateCountMap = new HashMap<>();

            for (Object dateObj : dates) {
                String date = dateObj.toString();
                List<Object> cachedData = oaCalendarCacheService.getCalendarDataByDate(date);
                int count = cachedData.size();
                totalCacheCount += count;
                dateCountMap.put(date, count);
            }

            log.info("缓存中共有{}条数据待处理", totalCacheCount);

            // 3. 安全检查：确保不会因为缓存问题导致数据丢失
            if (totalCacheCount == 0) {
                return EruptApiModel.errorApi("缓存数据异常：虽然有日期记录，但没有实际数据");
            }

            // 4. 获取并清理当月数据
            List<OaCalendarRequest> existingData = oaCalendarService.getCurrentMonthData();
            if (!existingData.isEmpty()) {
                log.info("开始清理{}条当月数据", existingData.size());
                for (OaCalendarRequest data : existingData) {
                    oaCalendarService.deleteCalendar(data);
                }
                log.info("当月数据清理完成");
            }

            // 5. 处理缓存数据
            for (Object dateObj : dates) {
                String date = dateObj.toString();
                List<Object> cachedData = oaCalendarCacheService.getCalendarDataByDate(date);
                log.info("开始处理{}的{}条数据", date, dateCountMap.get(date));

                for (Object data : cachedData) {
                    totalResult.incrementTotal();
                    try {
                        AnalyzeOaInfoRequest request = JSON.parseObject(data.toString(), AnalyzeOaInfoRequest.class);
                        String result = putOaCalendarSchedule(JSON.toJSONString(request));

                        if ("success".equals(result)) {
                            totalResult.incrementSuccess();
                        } else if ("已存在".equals(result)) {
                            totalResult.addSkipped(extractSubject(request.getCalendarSchedule()));
                        } else {
                            totalResult.addFailed(extractSubject(request.getCalendarSchedule()), result);
                        }
                    } catch (Exception e) {
                        log.error("处理单条数据失败: {}", e.getMessage());
                        totalResult.addFailed("处理失败", e.getMessage());
                        throw e; // 抛出异常触发事务回滚
                    }
                }

                oaCalendarCacheService.clearCache(date);
                log.info("{}的数据处理完成并清理缓存", date);
            }

            // 6. 结果检查
            if (!totalResult.getFailedItems().isEmpty()) {
                throw new RuntimeException("存在处理失败的记录，触发回滚");
            }

            if (totalResult.getSuccessCount() == 0) {
                throw new RuntimeException("没有成功处理任何数据，可能存在问题，触发回滚");
            }

            return EruptApiModel.successApi(String.format(
                    "处理完成 - 原有数据: %d条, 缓存数据: %d条, %s",
                    existingData.size(),
                    totalCacheCount,
                    totalResult.getSummary()
            ), totalResult);

        } catch (Exception e) {
            log.error("批量处理失败，执行回滚: {}", e.getMessage());
            throw new RuntimeException("批量处理失败，已回滚: " + e.getMessage());
        }
    }

    /**
     * 从日程信息中提取日期
     */
    private String extractDateFromSchedule(String schedule) {
        try {
            List<String> list = JSON.parseArray(schedule, String.class);

            for (String item : list) {
                if (DateTimeUtils.isValidTimeString(item)) {
                    DateTimeUtils.TimeResult timeResult = DateTimeUtils.parseTimeString(item);
                    // 检查是否是跨天日期
                    if (timeResult.isDateRange() && !timeResult.getDateRangeResults().isEmpty()) {
                        // 如果是跨天日期，返回多个日期，用逗号分隔
                        StringBuilder dates = new StringBuilder(timeResult.getDateStr());
                        for (DateTimeUtils.TimeResult dayResult : timeResult.getDateRangeResults()) {
                            dates.append(",").append(dayResult.getDateStr());
                        }
                        return dates.toString();
                    }
                    return timeResult.getDateStr();
                }
            }
            throw new RuntimeException("无法从日程中提取日期");
        } catch (Exception e) {
            log.error("提取日期失败", e);
            throw new RuntimeException("提取日期失败: " + e.getMessage());
        }
    }

    /**
     * 从日程信息中提取主题
     */
    private String extractSubject(String schedule) {
        try {
            List<String> list = JSON.parseArray(schedule, String.class);
            if (!list.isEmpty()) {
                return list.get(0);
            }
            return "未知主题";
        } catch (Exception e) {
            return "解析失败的主题";
        }
    }

    /**
     * DingTalk API
     */
    @Transactional
    @RequestMapping("oa/getUserInfo")
    public EruptApiModel getUserInfo(String code) {
        // 获取access_token
        String accessTokenBody = getDingTalkAccessToken();
        String accessToken;
        try {
            accessToken = JSON.parseObject(accessTokenBody).getString("access_token");
        } catch (Exception e) {
            String errmsg = JSON.parseObject(accessTokenBody).get("errmsg").toString();
            return EruptApiModel.errorApi(errmsg);
        }
        // 获取用户信息
        String userInfoBody = getUserInfoByCode(code, accessToken);
        String userId;
        try {
            userId = JSON.parseObject(userInfoBody).getJSONObject("result").getString("userid");
        } catch (Exception e) {
            String errmsg = JSON.parseObject(userInfoBody).get("errmsg").toString();
            return EruptApiModel.errorApi(errmsg);
        }
        // 获取用户详情
        String userDetailBody = getUserDetail(userId, accessToken);
        String name;
        String mobile;
        try {
            JSONObject res = JSON.parseObject(userDetailBody).getJSONObject("result");
            name = res.getString("name");
            mobile = res.getString("mobile");
        } catch (Exception e) {
            String errmsg = JSON.parseObject(userDetailBody).get("errmsg").toString();
            return EruptApiModel.errorApi(errmsg);
        }
        Map map = new HashMap();
        map.put("name", name);
        map.put("mobile", mobile);
        return EruptApiModel.successApi(map);
    }

    /**
     * DingTalk API
     * 获取企业内部应用的access_token
     */
    private String getDingTalkAccessToken() {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/gettoken");
        OapiGettokenRequest request = new OapiGettokenRequest();
        // 获取环境变量中的appKey和appSecret
        String appKey = System.getenv("APP_KEY");
        String appSecret = System.getenv("APP_SECRET");
        request.setAppkey(appKey);
        request.setAppsecret(appSecret);
        request.setHttpMethod("GET");
        OapiGettokenResponse response = null;
        try {
            response = client.execute(request);
        } catch (ApiException e) {
            throw new RuntimeException(e);
        }
        return response.getBody();
    }

    /**
     * DingTalk API
     * 通过免登码获取用户信息
     */
    private String getUserInfoByCode(String code, String accessToken) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/getuserinfo");
        OapiV2UserGetuserinfoRequest req = new OapiV2UserGetuserinfoRequest();
        req.setCode(code);
        OapiV2UserGetuserinfoResponse rsp = null;
        try {
            rsp = client.execute(req, accessToken);
        } catch (ApiException e) {
            throw new RuntimeException(e);
        }
        return rsp.getBody();
    }

    /**
     * DingTalk API
     * 查询用户详情
     */
    private String getUserDetail(String userId, String accessToken) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/get");
        OapiV2UserGetRequest req = new OapiV2UserGetRequest();
        req.setUserid(userId);
        req.setLanguage("zh_CN");
        OapiV2UserGetResponse rsp = null;
        try {
            rsp = client.execute(req, accessToken);
        } catch (ApiException e) {
            throw new RuntimeException(e);
        }
        return rsp.getBody();
    }

    // 将中文数字转换为整数值用于排序
    private int getChineseNumberValue(String chineseNumber) {
        try {
            // 尝试直接解析为整数（对于阿拉伯数字情况）
            return Integer.parseInt(chineseNumber);
        } catch (NumberFormatException e) {
            // 处理中文数字
            if ("零".equals(chineseNumber)) return 0;
            if ("一".equals(chineseNumber)) return 1;
            if ("二".equals(chineseNumber)) return 2;
            if ("三".equals(chineseNumber)) return 3;
            if ("四".equals(chineseNumber)) return 4;
            if ("五".equals(chineseNumber)) return 5;
            if ("六".equals(chineseNumber)) return 6;
            if ("七".equals(chineseNumber)) return 7;
            if ("八".equals(chineseNumber)) return 8;
            if ("九".equals(chineseNumber)) return 9;
            if ("十".equals(chineseNumber)) return 10;

            // 处理十一到十九
            if (chineseNumber.startsWith("十") && chineseNumber.length() > 1) {
                String secondChar = chineseNumber.substring(1);
                return 10 + getChineseNumberValue(secondChar);
            }

            // 处理二十到九十九
            if (chineseNumber.contains("十")) {
                String[] parts = chineseNumber.split("十", 2);
                int tens = getChineseNumberValue(parts[0]) * 10;
                int ones = parts.length > 1 && !parts[1].isEmpty() ? getChineseNumberValue(parts[1]) : 0;
                return tens + ones;
            }

            // 处理一百
            if (chineseNumber.equals("一百")) return 100;

            // 默认返回很大的值，确保无法识别的排在最后
            return Integer.MAX_VALUE;
        }
    }

    @RequestMapping("/sales/getUserVisit")
    public JSONArray saleGetUserVisit(String name,String type){
        JSONArray json = new JSONArray();
        List<PutOaInfoRequest> putOaInfoRequests;
        String format;
        SimpleDateFormat simpleDateFormat;
        if ("1".equals(type)) {
            simpleDateFormat = new SimpleDateFormat(("yyyy-MM-dd"));
        }else {
            simpleDateFormat = new SimpleDateFormat(("yyyy-MM"));
        }
        format = simpleDateFormat.format(new Date());
        if (name.equals("all")){
            putOaInfoRequests = eruptDao.queryEntityList(PutOaInfoRequest.class, "approval_status NOT LIKE '%拒绝%' AND approval_status NOT LIKE '%已撤销%' AND start_time LIKE '" + format + "%'");
        }else {
            putOaInfoRequests = eruptDao.queryEntityList(PutOaInfoRequest.class, "approval_status NOT LIKE '%拒绝%' AND approval_status NOT LIKE '%已撤销%' AND submitter = '" + name + "' AND start_time LIKE '" + format + "%'");
        }
        if (!putOaInfoRequests.isEmpty()){
            for (PutOaInfoRequest putOaInfoRequest : putOaInfoRequests) {
                JSONObject jsonOb = new JSONObject();
                jsonOb.put("reason",putOaInfoRequest.getTripReason());
                jsonOb.put("start",putOaInfoRequest.getStartTime());
                jsonOb.put("end",putOaInfoRequest.getEndTime());
                jsonOb.put("name",putOaInfoRequest.getSubmitter());
                jsonOb.put("companyAddress",putOaInfoRequest.getCompanyAddress());
                jsonOb.put("companyName",putOaInfoRequest.getCompanyName());
                json.add(jsonOb);
                if (putOaInfoRequest.getMultipleCompanies() != null && !putOaInfoRequest.getMultipleCompanies().isEmpty()){
                    JSONArray arrays = JSONArray.parseArray(putOaInfoRequest.getMultipleCompanies());
                    for(Object obj : arrays){
                        JSONObject jsonObs = new JSONObject();
                        JSONObject local = JSONObject.parseObject(obj.toString());
                        jsonObs.put("reason",local.getString("outReason"));
                        jsonObs.put("start",putOaInfoRequest.getStartTime());
                        jsonObs.put("end",putOaInfoRequest.getEndTime());
                        jsonObs.put("name",putOaInfoRequest.getSubmitter());
                        jsonObs.put("companyAddress",local.getString("address"));
                        jsonObs.put("companyName",local.getString("name"));
                        json.add(jsonObs);
                    }
                }
            }
        }
        return json;
    }

    /**
     * Get employee schedules
     */
    @RequestMapping("oa/employeeSchedule")
    public EruptApiModel getEmployeeSchedule(@RequestBody(required = false) EmployeeScheduleRequest request,
                                             @RequestParam(required = false) Boolean getEmployeesOnly) {
        try {
            // 如果只是获取员工列表
            if (getEmployeesOnly != null && getEmployeesOnly) {
                List<String> employees = employeeScheduleService.getAllEmployeeNames();
                return EruptApiModel.successApi(employees);
            }

            // 获取完整的日程数据
            if (request == null) {
                request = new EmployeeScheduleRequest();
            }
            EmployeeScheduleResponse response = employeeScheduleService.getEmployeeSchedules(request);
            return EruptApiModel.successApi(response);
        } catch (Exception e) {
            log.error("获取员工日程失败: {}", e.getMessage(), e);
            return EruptApiModel.errorApi("获取员工日程失败: " + e.getMessage());
        }
    }
}
